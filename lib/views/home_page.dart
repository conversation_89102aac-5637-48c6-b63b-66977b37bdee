import 'package:flutter/material.dart';
import '../viewmodels/home_viewmodel.dart';
import 'inventory_overview_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final HomeViewModel _viewModel = HomeViewModel();
  int _selectedIndex = 0;
  int _selectedTabIndex = 0; // 0: 总览, 1: 统计报表, 2: 库存预警

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              _buildHeader(),
              _buildTabContent(),
              const SizedBox(height: 100), // 为底部导航栏留出空间
            ],
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildHeader() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              IconButton(
                icon: const Icon(
                  Icons.arrow_back_ios,
                  color: Colors.black87,
                  size: 20,
                ),
                onPressed: () {},
              ),
              const Text(
                '仓库管理',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(
                      Icons.search,
                      color: Colors.black54,
                      size: 22,
                    ),
                    onPressed: () {},
                  ),
                  Stack(
                    children: [
                      IconButton(
                        icon: const Icon(
                          Icons.notifications_outlined,
                          color: Colors.black54,
                          size: 22,
                        ),
                        onPressed: () {},
                      ),
                      Positioned(
                        right: 8,
                        top: 8,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: const BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ],
                  ),
                  IconButton(
                    icon: const Icon(
                      Icons.menu,
                      color: Colors.black54,
                      size: 22,
                    ),
                    onPressed: () {},
                  ),
                ],
              ),
            ],
          ),
          // 添加标签页
          Container(
            margin: const EdgeInsets.only(top: 8),
            child: Row(
              children: [
                _buildTabItem('总览', 0),
                const SizedBox(width: 32),
                _buildTabItem('统计报表', 1),
                const SizedBox(width: 32),
                _buildTabItem('库存预警', 2),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabItem(String title, int index) {
    final isSelected = _selectedTabIndex == index;
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTabIndex = index;
        });
      },
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              color: isSelected ? Colors.black87 : Colors.black54,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            height: 2,
            width: 24,
            color: isSelected ? Colors.black87 : Colors.transparent,
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent() {
    switch (_selectedTabIndex) {
      case 0:
        return _buildOverviewTab();
      case 1:
        return _buildStatisticsTab();
      case 2:
        return _buildWarningTab();
      default:
        return _buildOverviewTab();
    }
  }

  Widget _buildOverviewTab() {
    return Column(
      children: [
        _buildMainCard(),
        _buildFunctionGrid(),
        _buildRecentActivities(),
      ],
    );
  }

  Widget _buildStatisticsTab() {
    return Column(
      children: [
        _buildStatisticsHeader(),
        _buildChartSection(),
        _buildStatisticsGrid(),
        _buildTrendAnalysis(),
      ],
    );
  }

  Widget _buildWarningTab() {
    return Column(children: [_buildWarningHeader(), _buildWarningList()]);
  }

  Widget _buildMainCard() {
    final data = _viewModel.warehouseData;
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 主要数据展示卡片
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      '库存总价值',
                      style: TextStyle(fontSize: 14, color: Colors.black54),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: const Text(
                        '今日',
                        style: TextStyle(fontSize: 12, color: Colors.black54),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  '¥${data.totalValue}',
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                    height: 1.2,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.trending_up, size: 16, color: Colors.green[600]),
                    const SizedBox(width: 4),
                    Text(
                      '+12.5% 较昨日',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.green[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          // 统计数据卡片
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () => _navigateToDetail('总商品数'),
                  child: _buildSmallStatCard(
                    '总商品数',
                    '${data.totalItems}',
                    '件',
                    Colors.blue[50]!,
                    Colors.blue,
                    Icons.inventory_2_outlined,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: GestureDetector(
                  onTap: () => _navigateToDetail('库存预警'),
                  child: _buildSmallStatCard(
                    '库存预警',
                    '${data.lowStockItems}',
                    '件',
                    Colors.orange[50]!,
                    Colors.orange,
                    Icons.warning_amber_outlined,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () => _navigateToDetail('今日入库'),
                  child: _buildSmallStatCard(
                    '今日入库',
                    '${data.todayInbound}',
                    '件',
                    Colors.green[50]!,
                    Colors.green,
                    Icons.arrow_downward,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: GestureDetector(
                  onTap: () => _navigateToDetail('今日出库'),
                  child: _buildSmallStatCard(
                    '今日出库',
                    '${data.todayOutbound}',
                    '件',
                    Colors.red[50]!,
                    Colors.red,
                    Icons.arrow_upward,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _navigateToDetail(String type) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            DetailPage(title: type, data: _viewModel.warehouseData),
      ),
    );
  }

  Widget _buildSmallStatCard(
    String title,
    String value,
    String unit,
    Color backgroundColor,
    Color? iconColor,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: backgroundColor,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, size: 18, color: iconColor),
              ),
              Icon(Icons.arrow_forward_ios, size: 12, color: Colors.grey[400]),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black54,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                value,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(width: 2),
              Text(
                unit,
                style: const TextStyle(fontSize: 12, color: Colors.black54),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionGrid() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '快捷功能',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              TextButton(
                onPressed: () {},
                child: Text(
                  '更多',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 4,
              childAspectRatio: 0.9,
              crossAxisSpacing: 16,
              mainAxisSpacing: 20,
            ),
            itemCount: _viewModel.functionItems.length,
            itemBuilder: (context, index) {
              final item = _viewModel.functionItems[index];
              return _buildFunctionItem(item.title, item.icon, item.color, () {
                ScaffoldMessenger.of(
                  context,
                ).showSnackBar(SnackBar(content: Text('点击了${item.title}')));
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFunctionItem(
    String title,
    String icon,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(icon, style: const TextStyle(fontSize: 24)),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black87,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivities() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '最近活动',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              TextButton(
                onPressed: () {},
                child: Text(
                  '查看全部',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: _viewModel.recentActivities.length,
            separatorBuilder: (context, index) => const SizedBox(height: 16),
            itemBuilder: (context, index) {
              final activity = _viewModel.recentActivities[index];
              return _buildActivityItem(activity);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(activity) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            width: 44,
            height: 44,
            decoration: BoxDecoration(
              color: activity.type == 'in'
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              activity.type == 'in'
                  ? Icons.arrow_downward_rounded
                  : Icons.arrow_upward_rounded,
              color: activity.type == 'in' ? Colors.green : Colors.red,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.title,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  activity.subtitle,
                  style: const TextStyle(fontSize: 12, color: Colors.black54),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                activity.amount,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: activity.type == 'in' ? Colors.green : Colors.red,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                activity.time,
                style: const TextStyle(fontSize: 12, color: Colors.black54),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 统计报表页面组件
  Widget _buildStatisticsHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 本月出库金额
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.03),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      '本月出库金额',
                      style: TextStyle(fontSize: 14, color: Colors.black54),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.red[50],
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        '4月',
                        style: TextStyle(fontSize: 12, color: Colors.red[700]),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                const Text(
                  '¥2,156,780',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                    height: 1.2,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.trending_up, size: 16, color: Colors.red[600]),
                    const SizedBox(width: 4),
                    Text(
                      '+12.8% 较上月',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          // 本月入库金额
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.03),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      '本月入库金额',
                      style: TextStyle(fontSize: 14, color: Colors.black54),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green[50],
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Text(
                        '4月',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.green[700],
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                const Text(
                  '¥2,890,450',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                    height: 1.2,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.trending_up, size: 16, color: Colors.green[600]),
                    const SizedBox(width: 4),
                    Text(
                      '+15.6% 较上月',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.green[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '趋势',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              Row(
                children: [
                  _buildChartLegend('入库数量', Colors.green),
                  const SizedBox(width: 16),
                  _buildChartLegend('出库数量', Colors.red),
                ],
              ),
            ],
          ),
          const SizedBox(height: 20),
          // 趋势图表区域
          Container(
            height: 200,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!, width: 1),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: CustomPaint(
                size: const Size(double.infinity, double.infinity),
                painter: TrendChartPainter(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartLegend(String label, Color color) {
    return Row(
      children: [
        Container(
          width: 12,
          height: 12,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.black54),
        ),
      ],
    );
  }

  Widget _buildStatisticsGrid() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  '日均出库',
                  '¥71,892',
                  '+8.3%',
                  Colors.red,
                  Icons.trending_up,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  '日均入库',
                  '¥96,348',
                  '+12.1%',
                  Colors.green,
                  Icons.trending_up,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  '库存品类',
                  '1,256',
                  '种商品',
                  Colors.blue,
                  Icons.category,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  '仓储利用率',
                  '78.5%',
                  '空间占用',
                  Colors.purple,
                  Icons.warehouse,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    String subtitle,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, size: 18, color: color),
              ),
              Icon(Icons.more_horiz, size: 16, color: Colors.grey[400]),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: const TextStyle(
              fontSize: 12,
              color: Colors.black54,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 2),
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 11,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrendAnalysis() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '趋势分析',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          _buildTrendItem(
            '库存流转',
            '本月入库金额较出库金额高73.4万元，库存总量呈上升趋势',
            Colors.green,
            Icons.trending_up,
          ),
          const SizedBox(height: 12),
          _buildTrendItem(
            '仓储效率',
            '仓储利用率达到78.5%，空间配置合理，建议适当扩充高频商品区域',
            Colors.blue,
            Icons.warehouse,
          ),
          const SizedBox(height: 12),
          _buildTrendItem(
            '库存结构',
            '库存品类增加至1,256种，商品种类丰富度提升，满足多样化需求',
            Colors.orange,
            Icons.category,
          ),
          const SizedBox(height: 12),
          _buildTrendItem(
            '预警管理',
            '当前23件商品库存偏低，其中5件严重不足，需及时补货',
            Colors.red,
            Icons.warning,
          ),
        ],
      ),
    );
  }

  Widget _buildTrendItem(
    String title,
    String description,
    Color color,
    IconData icon,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 16, color: color),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.black54,
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 库存预警页面组件
  Widget _buildWarningHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                '库存预警',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: Colors.black87,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  '23件商品',
                  style: TextStyle(fontSize: 12, color: Colors.red[700]),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildWarningStatItem('严重不足', '5', Colors.red),
              const SizedBox(width: 24),
              _buildWarningStatItem('库存偏低', '18', Colors.orange),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWarningStatItem(String label, String count, Color color) {
    return Row(
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(color: color, shape: BoxShape.circle),
        ),
        const SizedBox(width: 8),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.black54),
        ),
        const SizedBox(width: 4),
        Text(
          count,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: color,
          ),
        ),
      ],
    );
  }

  Widget _buildWarningList() {
    final warningItems = [
      {
        'name': 'iPhone 15 Pro Max',
        'current': '2',
        'min': '10',
        'level': 'high',
      },
      {'name': '小米电视65寸', 'current': '5', 'min': '15', 'level': 'medium'},
      {'name': 'MacBook Pro 14寸', 'current': '1', 'min': '8', 'level': 'high'},
      {
        'name': '华为FreeBuds Pro',
        'current': '8',
        'min': '20',
        'level': 'medium',
      },
      {'name': 'iPad Air 第5代', 'current': '3', 'min': '12', 'level': 'high'},
    ];

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '预警商品列表',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: warningItems.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final item = warningItems[index];
              return _buildWarningItem(item);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildWarningItem(Map<String, String> item) {
    final isHighLevel = item['level'] == 'high';
    final warningColor = isHighLevel ? Colors.red : Colors.orange;

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: warningColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: warningColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: warningColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              isHighLevel ? Icons.error : Icons.warning,
              size: 20,
              color: warningColor,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item['name']!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.black87,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '当前库存: ${item['current']} / 最低库存: ${item['min']}',
                  style: const TextStyle(fontSize: 12, color: Colors.black54),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: warningColor,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              isHighLevel ? '严重' : '偏低',
              style: const TextStyle(
                fontSize: 10,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      backgroundColor: Colors.white,
      selectedItemColor: Colors.blue,
      unselectedItemColor: Colors.grey,
      currentIndex: _selectedIndex,
      onTap: (index) {
        if (index == 1) {
          // 点击库存标签，跳转到库存总览页面
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => const InventoryOverviewPage(),
            ),
          );
        } else {
          setState(() {
            _selectedIndex = index;
          });
        }
      },
      items: const [
        BottomNavigationBarItem(icon: Icon(Icons.home), label: '首页'),
        BottomNavigationBarItem(icon: Icon(Icons.inventory), label: '库存'),
        BottomNavigationBarItem(icon: Icon(Icons.assessment), label: '统计'),
        BottomNavigationBarItem(icon: Icon(Icons.business), label: '资产'),
        BottomNavigationBarItem(icon: Icon(Icons.person), label: '我的'),
      ],
    );
  }
}

// 趋势图表绘制器
class TrendChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    // 假数据：30天的出入库数量 - 使用更平滑的数据
    final List<double> inboundData = [
      120,
      125,
      130,
      135,
      140,
      145,
      150,
      155,
      160,
      165,
      170,
      175,
      180,
      185,
      190,
      185,
      180,
      175,
      170,
      165,
      160,
      155,
      150,
      145,
      140,
      135,
      130,
      125,
      120,
      115,
    ];

    final List<double> outboundData = [
      100,
      105,
      110,
      115,
      120,
      125,
      130,
      135,
      140,
      145,
      150,
      155,
      160,
      165,
      170,
      165,
      160,
      155,
      150,
      145,
      140,
      135,
      130,
      125,
      120,
      115,
      110,
      105,
      100,
      95,
    ];

    final double chartWidth = size.width - 80; // 增加左边距
    final double chartHeight = size.height - 50;
    final double startX = 60; // 增加左边距
    final double startY = 20;

    // 找到最大值用于缩放，并添加一些padding
    final double maxValue =
        [...inboundData, ...outboundData].reduce((a, b) => a > b ? a : b) * 1.1;

    // 绘制背景渐变
    _drawBackground(canvas, startX, startY, chartWidth, chartHeight);

    // 绘制网格线（更细更淡）
    _drawGridLines(canvas, startX, startY, chartWidth, chartHeight);

    // 绘制数据区域填充
    _drawAreaFill(
      canvas,
      inboundData,
      startX,
      startY,
      chartWidth,
      chartHeight,
      maxValue,
      Colors.green.withValues(alpha: 0.1),
    );
    _drawAreaFill(
      canvas,
      outboundData,
      startX,
      startY,
      chartWidth,
      chartHeight,
      maxValue,
      Colors.red.withValues(alpha: 0.1),
    );

    // 绘制平滑曲线
    _drawSmoothLine(
      canvas,
      inboundData,
      startX,
      startY,
      chartWidth,
      chartHeight,
      maxValue,
      Colors.green,
      3.0,
    );
    _drawSmoothLine(
      canvas,
      outboundData,
      startX,
      startY,
      chartWidth,
      chartHeight,
      maxValue,
      Colors.red,
      3.0,
    );

    // 绘制坐标轴标签
    _drawLabels(
      canvas,
      size,
      startX,
      startY,
      chartWidth,
      chartHeight,
      maxValue,
    );
  }

  void _drawBackground(
    Canvas canvas,
    double startX,
    double startY,
    double chartWidth,
    double chartHeight,
  ) {
    final Paint backgroundPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [Colors.grey[50]!, Colors.white],
      ).createShader(Rect.fromLTWH(startX, startY, chartWidth, chartHeight));

    canvas.drawRect(
      Rect.fromLTWH(startX, startY, chartWidth, chartHeight),
      backgroundPaint,
    );
  }

  void _drawGridLines(
    Canvas canvas,
    double startX,
    double startY,
    double chartWidth,
    double chartHeight,
  ) {
    final Paint gridPaint = Paint()
      ..color = Colors.grey[100]!
      ..strokeWidth = 0.5;

    // 水平网格线（4条）
    for (int i = 1; i < 4; i++) {
      final double y = startY + (chartHeight / 4) * i;
      canvas.drawLine(
        Offset(startX, y),
        Offset(startX + chartWidth, y),
        gridPaint,
      );
    }

    // 垂直网格线（每5天一条，更少的线条）
    for (int i = 1; i < 5; i++) {
      final double x = startX + (chartWidth / 5) * i;
      canvas.drawLine(
        Offset(x, startY),
        Offset(x, startY + chartHeight),
        gridPaint,
      );
    }
  }

  void _drawAreaFill(
    Canvas canvas,
    List<double> data,
    double startX,
    double startY,
    double chartWidth,
    double chartHeight,
    double maxValue,
    Color fillColor,
  ) {
    final Paint fillPaint = Paint()
      ..color = fillColor
      ..style = PaintingStyle.fill;

    final Path fillPath = Path();

    // 从底部开始
    fillPath.moveTo(startX, startY + chartHeight);

    // 绘制数据点
    for (int i = 0; i < data.length; i++) {
      final double x = startX + (chartWidth / (data.length - 1)) * i;
      final double y =
          startY + chartHeight - (data[i] / maxValue) * chartHeight;

      if (i == 0) {
        fillPath.lineTo(x, y);
      } else {
        fillPath.lineTo(x, y);
      }
    }

    // 回到底部闭合路径
    fillPath.lineTo(startX + chartWidth, startY + chartHeight);
    fillPath.close();

    canvas.drawPath(fillPath, fillPaint);
  }

  void _drawSmoothLine(
    Canvas canvas,
    List<double> data,
    double startX,
    double startY,
    double chartWidth,
    double chartHeight,
    double maxValue,
    Color lineColor,
    double strokeWidth,
  ) {
    final Paint linePaint = Paint()
      ..color = lineColor
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final Paint pointPaint = Paint()
      ..color = lineColor
      ..style = PaintingStyle.fill;

    final Paint shadowPaint = Paint()
      ..color = lineColor.withValues(alpha: 0.3)
      ..strokeWidth = strokeWidth + 2
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final Path path = Path();
    final Path shadowPath = Path();

    for (int i = 0; i < data.length; i++) {
      final double x = startX + (chartWidth / (data.length - 1)) * i;
      final double y =
          startY + chartHeight - (data[i] / maxValue) * chartHeight;

      if (i == 0) {
        path.moveTo(x, y);
        shadowPath.moveTo(x, y + 2); // 阴影偏移
      } else {
        path.lineTo(x, y);
        shadowPath.lineTo(x, y + 2);
      }

      // 绘制数据点（更小更精致）
      if (i % 3 == 0) {
        // 每3个点显示一个
        canvas.drawCircle(Offset(x, y), 2.5, pointPaint);
        canvas.drawCircle(Offset(x, y), 1.5, Paint()..color = Colors.white);
      }
    }

    // 先绘制阴影
    canvas.drawPath(shadowPath, shadowPaint);
    // 再绘制主线条
    canvas.drawPath(path, linePaint);
  }

  void _drawLabels(
    Canvas canvas,
    Size size,
    double startX,
    double startY,
    double chartWidth,
    double chartHeight,
    double maxValue,
  ) {
    final TextStyle labelStyle = TextStyle(
      color: Colors.grey[600],
      fontSize: 9,
      fontWeight: FontWeight.w400,
    );

    // Y轴标签（数量）- 减少标签数量，避免遮挡
    for (int i = 0; i <= 4; i++) {
      final double value = (maxValue / 4) * (4 - i);
      final double y = startY + (chartHeight / 4) * i;

      final TextPainter textPainter = TextPainter(
        text: TextSpan(text: value.toInt().toString(), style: labelStyle),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      // 增加左边距，避免文字被截断
      textPainter.paint(canvas, Offset(startX - 45, y - 6));
    }

    // X轴标签（日期）- 简化标签
    final List<String> dateLabels = ['1日', '8日', '15日', '22日', '30日'];
    for (int i = 0; i < dateLabels.length; i++) {
      final double x = startX + (chartWidth / 4) * i;

      final TextPainter textPainter = TextPainter(
        text: TextSpan(text: dateLabels[i], style: labelStyle),
        textDirection: TextDirection.ltr,
      );
      textPainter.layout();
      textPainter.paint(canvas, Offset(x - 8, startY + chartHeight + 8));
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

// 详细数据页面
class DetailPage extends StatelessWidget {
  final String title;
  final dynamic data;

  const DetailPage({super.key, required this.title, required this.data});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.black87,
            size: 20,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            _buildDetailHeader(),
            const SizedBox(height: 16),
            _buildDetailList(),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailHeader() {
    String value = '';
    String description = '';
    Color color = Colors.blue;
    IconData icon = Icons.info;

    switch (title) {
      case '总商品数':
        value = '${data.totalItems}件';
        description = '当前仓库中所有商品的总数量';
        color = Colors.blue;
        icon = Icons.inventory_2_outlined;
        break;
      case '库存预警':
        value = '${data.lowStockItems}件';
        description = '库存数量低于安全库存的商品';
        color = Colors.orange;
        icon = Icons.warning_amber_outlined;
        break;
      case '今日入库':
        value = '${data.todayInbound}件';
        description = '今日新增入库的商品数量';
        color = Colors.green;
        icon = Icons.arrow_downward;
        break;
      case '今日出库':
        value = '${data.todayOutbound}件';
        description = '今日出库的商品数量';
        color = Colors.red;
        icon = Icons.arrow_upward;
        break;
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(icon, size: 32, color: color),
          ),
          const SizedBox(height: 16),
          Text(
            value,
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            description,
            style: const TextStyle(fontSize: 14, color: Colors.black54),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailList() {
    List<Map<String, dynamic>> items = [];

    switch (title) {
      case '总商品数':
        items = [
          {
            'name': 'iPhone 15 Pro Max',
            'category': '手机',
            'stock': 45,
            'status': 'normal',
          },
          {
            'name': '小米电视65寸',
            'category': '电视',
            'stock': 23,
            'status': 'normal',
          },
          {
            'name': 'MacBook Pro 14寸',
            'category': '电脑',
            'stock': 12,
            'status': 'normal',
          },
          {
            'name': '华为FreeBuds Pro',
            'category': '耳机',
            'stock': 67,
            'status': 'normal',
          },
          {
            'name': 'iPad Air 第5代',
            'category': '平板',
            'stock': 34,
            'status': 'normal',
          },
        ];
        break;
      case '库存预警':
        items = [
          {
            'name': 'iPhone 15 Pro Max',
            'current': 2,
            'min': 10,
            'level': 'high',
          },
          {'name': '小米电视65寸', 'current': 5, 'min': 15, 'level': 'medium'},
          {'name': 'MacBook Pro 14寸', 'current': 1, 'min': 8, 'level': 'high'},
          {
            'name': '华为FreeBuds Pro',
            'current': 8,
            'min': 20,
            'level': 'medium',
          },
          {'name': 'iPad Air 第5代', 'current': 3, 'min': 12, 'level': 'high'},
        ];
        break;
      case '今日入库':
        items = [
          {
            'name': 'iPhone 15 Pro Max',
            'quantity': 15,
            'time': '09:30',
            'supplier': '苹果官方',
          },
          {
            'name': '华为FreeBuds Pro',
            'quantity': 20,
            'time': '10:15',
            'supplier': '华为官方',
          },
          {
            'name': 'iPad Air 第5代',
            'quantity': 10,
            'time': '14:20',
            'supplier': '苹果官方',
          },
        ];
        break;
      case '今日出库':
        items = [
          {
            'name': '小米电视65寸',
            'quantity': 8,
            'time': '08:45',
            'customer': '张先生',
          },
          {
            'name': 'MacBook Pro 14寸',
            'quantity': 3,
            'time': '11:30',
            'customer': '李女士',
          },
          {
            'name': 'iPhone 15 Pro Max',
            'quantity': 12,
            'time': '15:10',
            'customer': '王先生',
          },
          {
            'name': '华为FreeBuds Pro',
            'quantity': 9,
            'time': '16:25',
            'customer': '陈女士',
          },
        ];
        break;
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.03),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Text(
              '详细列表',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: items.length,
            separatorBuilder: (context, index) => Divider(
              height: 1,
              color: Colors.grey[200],
              indent: 20,
              endIndent: 20,
            ),
            itemBuilder: (context, index) {
              return _buildDetailItem(items[index]);
            },
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }

  Widget _buildDetailItem(Map<String, dynamic> item) {
    switch (title) {
      case '总商品数':
        return ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 8,
          ),
          leading: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.blue.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.inventory_2, color: Colors.blue, size: 20),
          ),
          title: Text(
            item['name'],
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          subtitle: Text('分类: ${item['category']}'),
          trailing: Text(
            '${item['stock']}件',
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
        );
      case '库存预警':
        final isHigh = item['level'] == 'high';
        final color = isHigh ? Colors.red : Colors.orange;
        return ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 8,
          ),
          leading: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              isHigh ? Icons.error : Icons.warning,
              color: color,
              size: 20,
            ),
          ),
          title: Text(
            item['name'],
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          subtitle: Text('当前: ${item['current']} / 最低: ${item['min']}'),
          trailing: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              isHigh ? '严重' : '偏低',
              style: const TextStyle(
                fontSize: 10,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      case '今日入库':
        return ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 8,
          ),
          leading: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.arrow_downward,
              color: Colors.green,
              size: 20,
            ),
          ),
          title: Text(
            item['name'],
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          subtitle: Text('供应商: ${item['supplier']} • ${item['time']}'),
          trailing: Text(
            '+${item['quantity']}件',
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.green,
            ),
          ),
        );
      case '今日出库':
        return ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 8,
          ),
          leading: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.arrow_upward, color: Colors.red, size: 20),
          ),
          title: Text(
            item['name'],
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          subtitle: Text('客户: ${item['customer']} • ${item['time']}'),
          trailing: Text(
            '-${item['quantity']}件',
            style: const TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.red,
            ),
          ),
        );
      default:
        return const SizedBox();
    }
  }
}
